{"name": "raw-image-viewer", "displayName": "RawImageViewer", "description": "Advanced RAW image viewer with smart resolution recommendations and multiple pixel format support", "version": "0.0.3", "publisher": "YoungHong1992", "engines": {"vscode": "^1.91.0"}, "repository": {"github": "https://github.com/YoungHong1992/raw-image-viewer"}, "categories": ["Other"], "activationEvents": [], "main": "./out/extension.js", "contributes": {"customEditors": [{"viewType": "raw-image-viewer.rawImage", "displayName": "Raw Image Viewer", "selector": [{"filenamePattern": "*.raw"}]}]}, "scripts": {"vscode:prepublish": "npm run build-extension && npm run build-webview", "compile": "tsc -p ./", "watch-extension": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "vscode-test", "dev-webview": "vite --config vite.config.js", "build-webview": "vite build --config vite.config.js", "build-extension": "npm run compile", "build-release": "npx vsce package"}, "devDependencies": {"@types/mocha": "^10.0.7", "@types/node": "20.x", "@types/vscode": "^1.91.0", "@typescript-eslint/eslint-plugin": "^7.14.1", "@typescript-eslint/parser": "^7.11.0", "@vitejs/plugin-vue": "^5.0.5", "@vscode/test-cli": "^0.0.9", "@vscode/test-electron": "^2.4.0", "eslint": "^8.57.0", "typescript": "^5.4.5", "vite": "^5.4.19", "vue": "^3.4.27"}}
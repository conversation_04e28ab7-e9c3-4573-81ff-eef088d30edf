import * as assert from "assert";
import * as vscode from "vscode";
import * as path from "path";

suite("Raw Image Viewer Extension Test Suite", () => {
  vscode.window.showInformationMessage("开始运行 Raw Image Viewer 测试");

  test("扩展应该被激活", async () => {
    const extension = vscode.extensions.getExtension("YoungHong1992.raw-image-viewer");
    assert.ok(extension, "扩展应该存在");

    if (!extension.isActive) {
      await extension.activate();
    }
    assert.ok(extension.isActive, "扩展应该被激活");
  });

  test("自定义编辑器应该被注册", () => {
    // 检查自定义编辑器是否注册
    const customEditors = vscode.window.registerCustomEditorProvider;
    assert.ok(customEditors, "自定义编辑器提供者应该可用");
  });

  test("基本数组操作测试", () => {
    assert.strictEqual(-1, [1, 2, 3].indexOf(5));
    assert.strictEqual(-1, [1, 2, 3].indexOf(0));
    assert.strictEqual(1, [1, 2, 3].indexOf(2));
  });

  test("文件大小格式化测试", () => {
    // 模拟文件大小格式化函数
    const formatFileSize = (bytes: number): string => {
      if (bytes === 0) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    };

    assert.strictEqual(formatFileSize(0), '0 B');
    assert.strictEqual(formatFileSize(1024), '1 KB');
    assert.strictEqual(formatFileSize(1024 * 1024), '1 MB');
    assert.strictEqual(formatFileSize(1024 * 1024 * 1024), '1 GB');
  });
});

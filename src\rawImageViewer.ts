import * as vscode from 'vscode';
import { Disposable, disposeAll } from './dispose';

class RawImageDocument extends Disposable implements vscode.CustomDocument {
    private readonly _uri: vscode.Uri;
    private _documentData: Uint8Array;

    private constructor(
        uri: vscode.Uri,
        initialContent: Uint8Array
    ) {
        super();
        this._uri = uri;
        this._documentData = initialContent;
    }

    static async create(
        uri: vscode.Uri
    ): Promise<RawImageDocument> {
        try {
            const fileData = await RawImageDocument.readFile(uri);
            return new RawImageDocument(uri, fileData);
        } catch (error) {
            console.error(`Failed to create document for ${uri.fsPath}:`, error);
            throw error;
        }
    }

    private static async readFile(uri: vscode.Uri): Promise<Uint8Array> {
        if (uri.scheme === 'untitled') {
            return new Uint8Array();
        }

        try {
            const fileData = await vscode.workspace.fs.readFile(uri);
            return new Uint8Array(fileData);
        } catch (error) {
            console.error(`Failed to read file ${uri.fsPath}:`, error);
            throw new Error(`无法读取文件: ${error instanceof Error ? error.message : '未知错误'}`);
        }
    }


    public get uri() { return this._uri; }
    public get documentData(): Uint8Array { return this._documentData; }

    private readonly _onDidDispose = this._register(new vscode.EventEmitter<void>());
    public readonly onDidDispose = this._onDidDispose.event;

    private readonly _onDidChangeDocument = this._register(new vscode.EventEmitter<{
        readonly content?: Uint8Array;
    }>());
    public readonly onDidChangeContent = this._onDidChangeDocument.event;

    dispose(): void {
        this._onDidDispose.fire();
        super.dispose();
    }
}

export class RawImageViewerProvider implements vscode.CustomReadonlyEditorProvider<RawImageDocument> {
    private static readonly viewType = 'raw-image-viewer.rawImage';
    constructor(private readonly _context: vscode.ExtensionContext) { }

    public static register(context: vscode.ExtensionContext): vscode.Disposable {
        return vscode.window.registerCustomEditorProvider(
            RawImageViewerProvider.viewType,
            new RawImageViewerProvider(context),
            {
                webviewOptions: {
                    retainContextWhenHidden: true,
                },
                supportsMultipleEditorsPerDocument: false,
            });
    }

    private readonly webviewPanelMap = new Map<string, vscode.WebviewPanel>();

    async openCustomDocument(
        uri: vscode.Uri,
        openContext: { backupId?: string },
        _token: vscode.CancellationToken
    ): Promise<RawImageDocument> {
        try {
            const document = await RawImageDocument.create(uri);
            return document;
        } catch (error) {
            console.error(`Failed to open custom document for ${uri.fsPath}:`, error);
            vscode.window.showErrorMessage(`无法打开文件 ${uri.fsPath}: ${error instanceof Error ? error.message : '未知错误'}`);
            throw error;
        }
    }

    async resolveCustomEditor(
        document: RawImageDocument,
        webviewPanel: vscode.WebviewPanel,
        _token: vscode.CancellationToken
    ): Promise<void> {
        this.webviewPanelMap.set(document.uri.toString(), webviewPanel);

        const mediaBuildPath = vscode.Uri.joinPath(this._context.extensionUri, 'dist', 'media');

        webviewPanel.webview.options = {
            enableScripts: true,
            localResourceRoots: [mediaBuildPath]
        };
        webviewPanel.webview.html = await this.getHtmlForWebview(webviewPanel.webview);

        webviewPanel.webview.onDidReceiveMessage(e => this.onMessage(document, webviewPanel, e));

        // Clean up resources when the panel is disposed
        webviewPanel.onDidDispose(() => {
            this.webviewPanelMap.delete(document.uri.toString());
        });
    }

    private async getHtmlForWebview(webview: vscode.Webview): Promise<string> {
        const extensionUri = this._context.extensionUri;

        const mediaBuildPath = vscode.Uri.joinPath(extensionUri, 'dist', 'media');
        const baseUri = webview.asWebviewUri(mediaBuildPath);

        const indexPath = vscode.Uri.joinPath(mediaBuildPath, 'index.html');

        let htmlContent: string;
        try {
            const indexFileContentBytes = await vscode.workspace.fs.readFile(indexPath);
            htmlContent = new TextDecoder().decode(indexFileContentBytes);
        } catch (e) {
            console.error(`Error reading Vite's index.html: ${indexPath.fsPath}`, e);
            const errorMessage = `
                <html>
                    <head><title>Raw Image Viewer - Error</title></head>
                    <body style="font-family: Arial, sans-serif; padding: 20px; background-color: #1e1e1e; color: #cccccc;">
                        <h2 style="color: #ff6b6b;">扩展加载错误</h2>
                        <p>无法找到 Vite 构建输出文件: <code>${indexPath.fsPath}</code></p>
                        <p>请确保 UI 已正确构建:</p>
                        <ol>
                            <li>在终端中运行: <code>npm run build-webview</code></li>
                            <li>或者运行: <code>npm run vscode:prepublish</code></li>
                        </ol>
                        <p style="color: #888; font-size: 12px;">错误详情: ${e instanceof Error ? e.message : '未知错误'}</p>
                    </body>
                </html>
            `;
            return errorMessage;
        }


        // Ensure the <base> tag is correctly set for webview resource loading
        const baseTag = `<base href="${baseUri}/">`;
        if (htmlContent.includes('<base href')) {
            htmlContent = htmlContent.replace(/<base href=".*?"\/?>/, baseTag);
        } else {
            htmlContent = htmlContent.replace('<head>', `<head>\n    ${baseTag}`);
        }

        return htmlContent;
    }

    private readonly _callbacks = new Map<number, (response: any) => void>();

    private postMessage(panel: vscode.WebviewPanel, type: string, body: any): void {
        panel.webview.postMessage({ type, body });
    }

    private onMessage(document: RawImageDocument, panel: vscode.WebviewPanel, message: any) {
        switch (message.type) {
            case 'ready':
                this.postMessage(panel, 'init', {
                    value: document.documentData,
                    editable: false
                });
                return;
            case 'response': {
                const callback = this._callbacks.get(message.requestId);
                callback?.(message.body);
                return;
            }
        }
    }
}